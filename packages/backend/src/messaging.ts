import {
  ConversionMessage,
  EmptyMessage,
  ErrorMessage,
  PluginSettings,
  SettingsChangedMessage,
} from 'types';
import { AIService } from './services/aiService';

const aiService = new AIService();

export const postBackendMessage = figma.ui.postMessage;

export const postEmptyMessage = () =>
  postBackendMessage({ type: 'empty' } as EmptyMessage);

export const postConversionComplete = async (
  conversionData: ConversionMessage | Omit<ConversionMessage, 'type'>,
  figmaSelection: SceneNode,
) => {
  try {
    const bytes = await figmaSelection.exportAsync({
      format: 'PNG',
      constraint: { type: 'SCALE', value: 2 },
    });
    const base64Image = figma.base64Encode(bytes);
    const imageUrl = `data:image/png;base64,${base64Image}`;

    const { cleanCode } = await aiService.cleanCode(
      conversionData.code,
      imageUrl,
    );

    const sections = parseAIResponse(cleanCode);

    postBackendMessage({
      ...conversionData,
      code: sections.code,
      config: sections.config || {},
      htmlPreview: {
        content: sections.code,
        size: figmaSelection.absoluteBoundingBox || { width: 500, height: 500 },
      },
      image: imageUrl,
      type: 'code',
    });
  } catch (error) {
    console.error('AI processing failed:', error);
    postBackendMessage({
      ...conversionData,
      type: 'code',
      config: {},
    });
  }
};

/**
 * Removes fiddleArtifact tags and other interfering content from AI response
 */
function cleanAIResponse(response: string): string {
  return (
    response
      // Remove fiddleArtifact tags and their content
      .replace(/<fiddleArtifact>[\s\S]*?<\/fiddleArtifact>/g, '')
      // Remove any remaining artifact tags
      .replace(/<\/?fiddleArtifact[^>]*>/g, '')
      // Clean up extra whitespace
      .replace(/\n\s*\n\s*\n/g, '\n\n')
      .trim()
  );
}

/**
 * Extracts and cleans JSON configuration from the TWEAKPANE section
 */
function extractConfigFromSection(configSection: string): object {
  let configText = configSection
    // Remove markdown code blocks
    .replace(/```(?:json|language=json)?\n/g, '')
    .replace(/```\s*$/g, '')
    // Remove any remaining artifact tags
    .replace(/<\/?fiddleArtifact[^>]*>/g, '')
    // Clean whitespace
    .replace(/^\s*|\s*$/g, '')
    .trim();

  // If the config starts with a quote, it might be wrapped incorrectly
  // Try to extract just the JSON object
  const jsonMatch = configText.match(/\{[\s\S]*\}/);
  if (jsonMatch) {
    configText = jsonMatch[0];
  }

  return JSON.parse(configText);
}

function parseAIResponse(response: string) {
  // First, clean the response to remove interfering tags
  const cleanedResponse = cleanAIResponse(response);

  const codeSectionMatch = cleanedResponse.match(
    /---\s*CODE\s*---([\s\S]*?)(?=---\s*TWEAKPANE\s*---|$)/,
  );
  const configSectionMatch = cleanedResponse.match(
    /---\s*TWEAKPANE\s*---([\s\S]*?)$/,
  );

  const code = codeSectionMatch
    ? codeSectionMatch[1]
        .replace(/```(?:html|language=html)?\n/g, '')
        .replace(/```(?:language=)?\n?$/g, '')
        .trim()
    : '';

  let config = {};

  if (configSectionMatch) {
    try {
      const configText = configSectionMatch[1].trim();

      if (configText && configText !== '{}') {
        config = extractConfigFromSection(configText);
      }
    } catch (e) {
      console.error('Failed to parse config:', e);
      console.error('Problematic config text:', configSectionMatch[1]);
    }
  }

  return {
    code,
    config: config || {}, // Ensure config is always an object
  };
}

export const postError = (error: string) =>
  postBackendMessage({ type: 'error', error } as ErrorMessage);

export const postSettingsChanged = (settings: PluginSettings) =>
  postBackendMessage({
    type: 'pluginSettingsChanged',
    settings,
  } as SettingsChangedMessage);
