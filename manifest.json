{"name": "Fiddle: Prototype & Animate in Code", "id": "1465161213135613997", "api": "1.0.0", "main": "apps/plugin/dist/code.js", "ui": "apps/plugin/dist/index.html", "capabilities": ["inspect", "codegen", "vscode"], "enableProposedApi": false, "documentAccess": "dynamic-page", "editorType": ["figma", "dev"], "networkAccess": {"allowedDomains": ["https://api.imgur.com", "https://i.imgur.com", "https://anthropic-proxy-kappa.vercel.app"]}, "codegenLanguages": [{"label": "HTML", "value": "html"}, {"label": "React (JSX)", "value": "html_jsx"}, {"label": "Tailwind", "value": "tailwind"}, {"label": "Tailwind (JSX)", "value": "tailwind_jsx"}]}